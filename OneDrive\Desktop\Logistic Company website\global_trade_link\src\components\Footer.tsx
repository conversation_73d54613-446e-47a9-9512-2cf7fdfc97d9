import { Link } from 'react-router-dom';
import { Ship, Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-navy text-primary-foreground">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Ship className="h-8 w-8" />
              <span className="text-xl font-bold">GlobalTradeLink</span>
            </div>
            <p className="text-silver text-sm">
              Connecting markets worldwide with reliable import/export solutions and logistics excellence.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-silver hover:text-white transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
              <a href="#" className="text-silver hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-silver hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/services" className="text-silver hover:text-white transition-colors">
                  Our Services
                </Link>
              </li>
              <li>
                <Link to="/track" className="text-silver hover:text-white transition-colors">
                  Track Shipment
                </Link>
              </li>
              <li>
                <Link to="/coverage" className="text-silver hover:text-white transition-colors">
                  Global Coverage
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-silver hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Services</h3>
            <ul className="space-y-2">
              <li className="text-silver">Import & Export</li>
              <li className="text-silver">Freight Forwarding</li>
              <li className="text-silver">Customs Clearance</li>
              <li className="text-silver">Warehousing</li>
              <li className="text-silver">Supply Chain</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Us</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-silver" />
                <span className="text-silver">+****************</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-silver" />
                <span className="text-silver"><EMAIL></span>
              </div>
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 text-silver mt-1" />
                <span className="text-silver">
                  123 Trade Center Blvd<br />
                  Global Commerce City, GC 12345
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-silver/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-silver text-sm">
            © 2024 GlobalTradeLink. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/privacy" className="text-silver hover:text-white transition-colors text-sm">
              Privacy Policy
            </Link>
            <Link to="/terms" className="text-silver hover:text-white transition-colors text-sm">
              Terms & Conditions
            </Link>
            <select className="bg-transparent text-silver text-sm border border-silver/20 rounded px-2 py-1">
              <option value="en">English</option>
              <option value="fr">Français</option>
              <option value="es">Español</option>
              <option value="ar">العربية</option>
            </select>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;