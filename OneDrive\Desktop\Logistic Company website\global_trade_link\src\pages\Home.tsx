import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import TrackingSystem from '@/components/TrackingSystem';
import { 
  ArrowRight, 
  Shield, 
  Globe, 
  Clock, 
  Award,
  Truck,
  Ship,
  Plane,
  Building,
  Users,
  TrendingUp,
  Package
} from 'lucide-react';
import heroImage from '@/assets/hero-logistics.jpg';
import servicesImage from '@/assets/services-overview.jpg';

const Home = () => {
  const services = [
    {
      icon: Ship,
      title: 'Ocean Freight',
      description: 'Cost-effective sea freight solutions for large cargo volumes with global coverage.'
    },
    {
      icon: Plane,
      title: 'Air Freight',
      description: 'Fast and reliable air cargo services for time-sensitive shipments worldwide.'
    },
    {
      icon: Truck,
      title: 'Land Transport',
      description: 'Comprehensive ground transportation across continents with real-time tracking.'
    },
    {
      icon: Building,
      title: 'Warehousing',
      description: 'Secure storage facilities with inventory management and distribution services.'
    }
  ];

  const stats = [
    { number: '150+', label: 'Countries Served', icon: Globe },
    { number: '50K+', label: 'Shipments Delivered', icon: Package },
    { number: '25+', label: 'Years Experience', icon: Award },
    { number: '99.8%', label: 'On-Time Delivery', icon: Clock }
  ];

  const partners = [
    'DHL', 'FedEx', 'Maersk', 'COSCO', 'UPS', 'MSC'
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${heroImage})` }}
        />
        <div className="absolute inset-0 bg-navy/70" />
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <div className="max-w-4xl mx-auto space-y-8 fade-in">
            <h1 className="text-4xl md:text-6xl font-bold leading-tight">
              Connecting Markets.
              <br />
              <span className="text-silver">Delivering Trust.</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-silver max-w-2xl mx-auto">
              Your trusted partner for international import/export solutions with seamless logistics and real-time tracking.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-navy hover:bg-silver text-lg px-8 py-4">
                <Link to="/track" className="flex items-center">
                  Track Your Shipment
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-navy text-lg px-8 py-4">
                <Link to="/quote" className="flex items-center">
                  Request Quote
                </Link>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center space-y-2 slide-up">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <stat.icon className="h-6 w-6" />
                </div>
                <div className="text-3xl font-bold text-foreground">{stat.number}</div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Tracking Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Real-Time Shipment Tracking
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Monitor your cargo every step of the way with our advanced tracking system
            </p>
          </div>
          
          <TrackingSystem />
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  Comprehensive Logistics Solutions
                </h2>
                <p className="text-xl text-muted-foreground">
                  From origin to destination, we handle every aspect of your international trade requirements with precision and care.
                </p>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {services.map((service, index) => (
                  <Card key={index} className="card-elevated">
                    <CardContent className="p-6 space-y-4">
                      <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                        <service.icon className="h-6 w-6" />
                      </div>
                      <h3 className="text-lg font-semibold text-foreground">{service.title}</h3>
                      <p className="text-muted-foreground text-sm">{service.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              <Button asChild size="lg">
                <Link to="/services" className="flex items-center">
                  View All Services
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
            
            <div className="relative">
              <img 
                src={servicesImage} 
                alt="Global logistics services" 
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-navy/50 to-transparent rounded-lg" />
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Why Choose GlobalTradeLink?</h2>
            <p className="text-xl text-muted-foreground">
              Trusted by businesses worldwide for reliable and efficient logistics solutions
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <Card className="card-elevated text-center">
              <CardContent className="p-8 space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                  <Shield className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">Secure & Reliable</h3>
                <p className="text-muted-foreground">
                  End-to-end security and insurance coverage for complete peace of mind.
                </p>
              </CardContent>
            </Card>
            
            <Card className="card-elevated text-center">
              <CardContent className="p-8 space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                  <Globe className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">Global Network</h3>
                <p className="text-muted-foreground">
                  Extensive network covering 150+ countries with local expertise everywhere.
                </p>
              </CardContent>
            </Card>
            
            <Card className="card-elevated text-center">
              <CardContent className="p-8 space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                  <Clock className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">On-Time Delivery</h3>
                <p className="text-muted-foreground">
                  Industry-leading 99.8% on-time delivery rate with real-time tracking.
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Partners */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-muted-foreground mb-8">Trusted Partners</h3>
            <div className="flex flex-wrap justify-center items-center gap-8">
              {partners.map((partner, index) => (
                <Badge key={index} variant="outline" className="text-lg px-4 py-2">
                  {partner}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-navy text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Ready to Ship with Confidence?
            </h2>
            <p className="text-xl text-silver">
              Join thousands of satisfied customers who trust GlobalTradeLink for their international logistics needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-navy hover:bg-silver">
                <Link to="/quote">Get Free Quote</Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-navy">
                <Link to="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;