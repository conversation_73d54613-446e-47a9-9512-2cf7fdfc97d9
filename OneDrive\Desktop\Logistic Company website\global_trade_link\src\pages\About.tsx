import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Award, 
  Users, 
  Globe, 
  TrendingUp,
  Shield,
  Clock,
  Heart,
  Target,
  Eye,
  Star
} from 'lucide-react';

const About = () => {
  const stats = [
    { number: '25+', label: 'Years of Experience', icon: Award },
    { number: '500+', label: 'Expert Team Members', icon: Users },
    { number: '150+', label: 'Countries Served', icon: Globe },
    { number: '50K+', label: 'Successful Shipments', icon: TrendingUp }
  ];

  const values = [
    {
      icon: Shield,
      title: 'Trust & Reliability',
      description: 'We build lasting relationships through consistent, dependable service and transparent communication.'
    },
    {
      icon: Clock,
      title: 'Efficiency',
      description: 'Streamlined processes and cutting-edge technology ensure fast, accurate delivery every time.'
    },
    {
      icon: Heart,
      title: 'Customer Focus',
      description: 'Your success is our priority. We tailor solutions to meet your unique business requirements.'
    },
    {
      icon: Target,
      title: 'Excellence',
      description: 'We continuously improve our services to exceed expectations and industry standards.'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      position: 'Chief Executive Officer',
      experience: '15+ years in international trade',
      specialties: ['Strategic Leadership', 'Global Expansion', 'Client Relations']
    },
    {
      name: 'Marcus Rodriguez',
      position: 'Chief Operations Officer',
      experience: '12+ years in logistics operations',
      specialties: ['Supply Chain Optimization', 'Process Management', 'Quality Control']
    },
    {
      name: 'Dr. Priya Patel',
      position: 'Head of Technology',
      experience: '10+ years in logistics technology',
      specialties: ['Digital Innovation', 'Tracking Systems', 'Data Analytics']
    },
    {
      name: 'James Thompson',
      position: 'Director of Sales',
      experience: '14+ years in B2B sales',
      specialties: ['Client Development', 'Market Analysis', 'Partnership Building']
    }
  ];

  const milestones = [
    { year: '1999', event: 'Founded with vision to connect global markets' },
    { year: '2005', event: 'Expanded to 50 countries worldwide' },
    { year: '2012', event: 'Launched advanced tracking technology' },
    { year: '2018', event: 'Reached 100+ country coverage' },
    { year: '2020', event: 'Implemented AI-powered logistics solutions' },
    { year: '2024', event: 'Serving 150+ countries with 99.8% reliability' }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-16 bg-navy text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">About GlobalTradeLink</h1>
          <p className="text-xl text-silver max-w-3xl mx-auto">
            For over 25 years, we've been connecting businesses worldwide through reliable, efficient logistics solutions and unwavering commitment to excellence.
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                  <stat.icon className="h-8 w-8" />
                </div>
                <div className="text-3xl font-bold text-foreground">{stat.number}</div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <Card className="card-elevated">
              <CardContent className="p-8 space-y-4">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <Target className="h-6 w-6" />
                </div>
                <h2 className="text-2xl font-bold text-foreground">Our Mission</h2>
                <p className="text-muted-foreground leading-relaxed">
                  To bridge global markets by providing reliable, efficient, and innovative logistics solutions that empower businesses to grow internationally. We strive to make international trade accessible, transparent, and seamless for companies of all sizes.
                </p>
              </CardContent>
            </Card>

            <Card className="card-elevated">
              <CardContent className="p-8 space-y-4">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <Eye className="h-6 w-6" />
                </div>
                <h2 className="text-2xl font-bold text-foreground">Our Vision</h2>
                <p className="text-muted-foreground leading-relaxed">
                  To be the world's most trusted logistics partner, setting the standard for excellence in international trade. We envision a connected world where geographic boundaries don't limit business opportunities, enabled by our innovative solutions and exceptional service.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Our Values</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              These core principles guide everything we do and shape our relationships with clients, partners, and team members.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="card-elevated text-center">
                <CardContent className="p-6 space-y-4">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                    <value.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground">{value.title}</h3>
                  <p className="text-muted-foreground text-sm">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Our Journey</h2>
            <p className="text-xl text-muted-foreground">
              Key milestones in our 25-year journey of connecting global markets
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-center space-x-6">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white font-bold">
                      {milestone.year}
                    </div>
                  </div>
                  <Card className="flex-1 card-elevated">
                    <CardContent className="p-6">
                      <p className="text-foreground font-medium">{milestone.event}</p>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Leadership Team</h2>
            <p className="text-xl text-muted-foreground">
              Meet the experienced professionals leading GlobalTradeLink to new heights
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="card-elevated">
                <CardContent className="p-6 space-y-4">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <Users className="h-10 w-10 text-primary" />
                  </div>
                  <div className="text-center space-y-2">
                    <h3 className="text-lg font-semibold text-foreground">{member.name}</h3>
                    <p className="text-muted-foreground font-medium">{member.position}</p>
                    <p className="text-sm text-muted-foreground">{member.experience}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-foreground">Specialties:</p>
                    <div className="flex flex-wrap gap-2">
                      {member.specialties.map((specialty, specialtyIndex) => (
                        <Badge key={specialtyIndex} variant="outline" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications & Awards */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-foreground mb-8">Awards & Certifications</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            {['ISO 9001:2015', 'IATA Certified', 'C-TPAT Member', 'AEO Authorized'].map((cert, index) => (
              <Card key={index} className="card-elevated">
                <CardContent className="p-6 text-center space-y-4">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                    <Star className="h-6 w-6" />
                  </div>
                  <p className="font-medium text-foreground">{cert}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="max-w-2xl mx-auto space-y-6">
            <h3 className="text-xl font-semibold text-foreground">Ready to Work with Us?</h3>
            <p className="text-muted-foreground">
              Join thousands of satisfied customers who trust GlobalTradeLink for their international logistics needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">
                <Link to="/contact">Contact Our Team</Link>
              </Button>
              <Button size="lg" variant="outline">
                <Link to="/services">Explore Services</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;