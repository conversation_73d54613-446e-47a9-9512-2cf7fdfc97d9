@tailwind base;
@tailwind components;
@tailwind utilities;

/* GlobalTradeLink Design System - Professional logistics theme */

@layer base {
  :root {
    /* Brand Colors - Navy, Silver, White theme */
    --background: 0 0% 100%;
    --foreground: 220 100% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 100% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 100% 15%;

    /* Navy Blue Primary */
    --primary: 220 100% 25%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 220 100% 20%;

    /* Silver Secondary */
    --secondary: 215 25% 60%;
    --secondary-foreground: 220 100% 15%;

    --muted: 220 13% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 215 25% 60%;
    --accent-foreground: 220 100% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 100% 25%;

    /* Custom Brand Tokens */
    --navy: 220 100% 25%;
    --navy-light: 220 100% 30%;
    --navy-dark: 220 100% 20%;
    --silver: 215 25% 60%;
    --silver-light: 215 25% 70%;
    --trust-blue: 220 100% 25%;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--navy)) 0%, hsl(var(--navy-light)) 100%);
    --gradient-card: linear-gradient(145deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
    
    /* Shadows */
    --shadow-card: 0 4px 20px hsl(var(--navy) / 0.1);
    --shadow-hover: 0 8px 30px hsl(var(--navy) / 0.15);
    --shadow-soft: 0 2px 10px hsl(var(--navy) / 0.05);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

/* Custom Components */
@layer components {
  .hero-section {
    background: var(--gradient-hero);
  }
  
  .card-elevated {
    box-shadow: var(--shadow-card);
    transition: box-shadow 0.3s ease;
  }
  
  .card-elevated:hover {
    box-shadow: var(--shadow-hover);
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-navy-dark transition-all duration-300;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-silver-light transition-all duration-300;
  }
  
  .tracking-card {
    background: var(--gradient-card);
    box-shadow: var(--shadow-soft);
  }
  
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }
  
  .slide-up {
    animation: slideUp 0.8s ease-out;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}