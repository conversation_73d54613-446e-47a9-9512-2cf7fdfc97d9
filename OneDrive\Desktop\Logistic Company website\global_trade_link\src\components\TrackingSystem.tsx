import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Package, MapPin, Clock, CheckCircle, Truck, Ship, Plane } from 'lucide-react';
import trackingIllustration from '@/assets/tracking-illustration.jpg';

interface TrackingResult {
  trackingNumber: string;
  status: 'in-transit' | 'customs' | 'delivered' | 'processing';
  currentLocation: string;
  estimatedDelivery: string;
  lastUpdate: string;
  shipmentDetails: {
    origin: string;
    destination: string;
    carrier: string;
    service: string;
    weight: string;
  };
  timeline: Array<{
    status: string;
    location: string;
    date: string;
    completed: boolean;
  }>;
}

// Mock tracking data
const mockTrackingData: Record<string, TrackingResult> = {
  'GTL001234567': {
    trackingNumber: 'GTL001234567',
    status: 'in-transit',
    currentLocation: 'Hong Kong Port',
    estimatedDelivery: 'March 15, 2024',
    lastUpdate: 'Today, 2:30 PM',
    shipmentDetails: {
      origin: 'Shanghai, China',
      destination: 'Los Angeles, USA',
      carrier: 'Ocean Freight',
      service: 'Standard',
      weight: '2,500 lbs'
    },
    timeline: [
      { status: 'Order Processed', location: 'Shanghai, China', date: 'Mar 5, 2024', completed: true },
      { status: 'Shipped', location: 'Shanghai Port', date: 'Mar 6, 2024', completed: true },
      { status: 'In Transit', location: 'Hong Kong Port', date: 'Mar 8, 2024', completed: true },
      { status: 'Customs Clearance', location: 'Los Angeles Port', date: 'Mar 14, 2024', completed: false },
      { status: 'Out for Delivery', location: 'Los Angeles, USA', date: 'Mar 15, 2024', completed: false },
    ]
  },
  'GTL001234568': {
    trackingNumber: 'GTL001234568',
    status: 'delivered',
    currentLocation: 'Frankfurt, Germany',
    estimatedDelivery: 'Delivered',
    lastUpdate: 'Yesterday, 4:15 PM',
    shipmentDetails: {
      origin: 'New York, USA',
      destination: 'Frankfurt, Germany',
      carrier: 'Air Freight',
      service: 'Express',
      weight: '150 lbs'
    },
    timeline: [
      { status: 'Order Processed', location: 'New York, USA', date: 'Mar 3, 2024', completed: true },
      { status: 'Shipped', location: 'JFK Airport', date: 'Mar 3, 2024', completed: true },
      { status: 'In Transit', location: 'Frankfurt Airport', date: 'Mar 4, 2024', completed: true },
      { status: 'Customs Cleared', location: 'Frankfurt, Germany', date: 'Mar 4, 2024', completed: true },
      { status: 'Delivered', location: 'Frankfurt, Germany', date: 'Mar 5, 2024', completed: true },
    ]
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'in-transit':
      return <Ship className="h-5 w-5" />;
    case 'customs':
      return <Package className="h-5 w-5" />;
    case 'delivered':
      return <CheckCircle className="h-5 w-5" />;
    case 'processing':
      return <Clock className="h-5 w-5" />;
    default:
      return <Package className="h-5 w-5" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'in-transit':
      return 'bg-blue-500/10 text-blue-700 border-blue-200';
    case 'customs':
      return 'bg-orange-500/10 text-orange-700 border-orange-200';
    case 'delivered':
      return 'bg-green-500/10 text-green-700 border-green-200';
    case 'processing':
      return 'bg-gray-500/10 text-gray-700 border-gray-200';
    default:
      return 'bg-gray-500/10 text-gray-700 border-gray-200';
  }
};

const TrackingSystem = () => {
  const [trackingNumber, setTrackingNumber] = useState('');
  const [trackingResult, setTrackingResult] = useState<TrackingResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleTrack = async () => {
    if (!trackingNumber.trim()) {
      setError('Please enter a tracking number');
      return;
    }

    setIsLoading(true);
    setError('');
    
    // Simulate API call
    setTimeout(() => {
      const result = mockTrackingData[trackingNumber.toUpperCase()];
      if (result) {
        setTrackingResult(result);
        setError('');
      } else {
        setError('Tracking number not found. Try GTL001234567 or GTL001234568');
        setTrackingResult(null);
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-8">
      {/* Tracking Input */}
      <Card className="card-elevated">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl text-foreground">Track Your Shipment</CardTitle>
          <p className="text-muted-foreground">
            Enter your tracking number to get real-time updates on your shipment
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Enter tracking number (e.g., GTL001234567)"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                className="h-12"
                onKeyPress={(e) => e.key === 'Enter' && handleTrack()}
              />
            </div>
            <Button 
              onClick={handleTrack} 
              className="h-12 px-8"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Tracking...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Track Shipment
                </>
              )}
            </Button>
          </div>
          {error && <p className="text-destructive text-sm">{error}</p>}
        </CardContent>
      </Card>

      {/* Tracking Results */}
      {trackingResult && (
        <div className="space-y-6 fade-in">
          {/* Status Overview */}
          <Card className="card-elevated">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center space-y-2">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${getStatusColor(trackingResult.status)}`}>
                    {getStatusIcon(trackingResult.status)}
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <Badge className={getStatusColor(trackingResult.status)}>
                      {trackingResult.status.replace('-', ' ').toUpperCase()}
                    </Badge>
                  </div>
                </div>
                
                <div className="text-center space-y-2">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                    <MapPin className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Current Location</p>
                    <p className="font-medium">{trackingResult.currentLocation}</p>
                  </div>
                </div>

                <div className="text-center space-y-2">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                    <Clock className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Est. Delivery</p>
                    <p className="font-medium">{trackingResult.estimatedDelivery}</p>
                  </div>
                </div>

                <div className="text-center space-y-2">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                    <Package className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Last Update</p>
                    <p className="font-medium">{trackingResult.lastUpdate}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipment Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="card-elevated">
              <CardHeader>
                <CardTitle>Shipment Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Origin</p>
                    <p className="font-medium">{trackingResult.shipmentDetails.origin}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Destination</p>
                    <p className="font-medium">{trackingResult.shipmentDetails.destination}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Carrier</p>
                    <p className="font-medium">{trackingResult.shipmentDetails.carrier}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Service</p>
                    <p className="font-medium">{trackingResult.shipmentDetails.service}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tracking Timeline */}
            <Card className="card-elevated">
              <CardHeader>
                <CardTitle>Shipment Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {trackingResult.timeline.map((event, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className={`w-4 h-4 rounded-full ${event.completed ? 'bg-primary' : 'bg-muted'}`} />
                      <div className="flex-1">
                        <p className={`font-medium ${event.completed ? 'text-foreground' : 'text-muted-foreground'}`}>
                          {event.status}
                        </p>
                        <p className="text-sm text-muted-foreground">{event.location}</p>
                      </div>
                      <p className="text-sm text-muted-foreground">{event.date}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Demo Instructions */}
      {!trackingResult && !isLoading && (
        <Card className="bg-muted/30">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <img 
                src={trackingIllustration} 
                alt="Global tracking system" 
                className="w-full md:w-1/3 h-48 object-cover rounded-lg"
              />
              <div className="flex-1 space-y-4">
                <h3 className="text-xl font-semibold">Try Our Demo</h3>
                <p className="text-muted-foreground">
                  Test our tracking system with these sample tracking numbers:
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">GTL001234567</Badge>
                    <span className="text-sm text-muted-foreground">- Ocean freight shipment</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">GTL001234568</Badge>
                    <span className="text-sm text-muted-foreground">- Air freight delivery</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TrackingSystem