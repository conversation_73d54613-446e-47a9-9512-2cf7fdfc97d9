import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Ship, 
  Plane, 
  Truck, 
  Building, 
  FileText, 
  Users,
  Shield,
  Clock,
  Globe,
  ArrowRight
} from 'lucide-react';
import servicesImage from '@/assets/services-overview.jpg';

const Services = () => {
  const services = [
    {
      icon: Ship,
      title: 'Ocean Freight',
      description: 'Comprehensive sea freight solutions for all cargo types with competitive rates and reliable scheduling.',
      features: ['FCL & LCL options', 'Door-to-door service', 'Cargo insurance', 'Real-time tracking']
    },
    {
      icon: Plane,
      title: 'Air Freight',
      description: 'Fast and secure air cargo services for time-sensitive shipments worldwide.',
      features: ['Express delivery', 'Temperature-controlled', 'Dangerous goods handling', 'Charter services']
    },
    {
      icon: Truck,
      title: 'Land Transport',
      description: 'Reliable ground transportation across continents with flexible routing options.',
      features: ['FTL & LTL services', 'Cross-border transport', 'Last-mile delivery', 'Vehicle tracking']
    },
    {
      icon: Building,
      title: 'Warehousing & Distribution',
      description: 'Secure storage facilities with advanced inventory management and distribution services.',
      features: ['Climate-controlled storage', 'Inventory management', 'Pick & pack services', 'Distribution network']
    },
    {
      icon: FileText,
      title: 'Customs Clearance',
      description: 'Expert customs brokerage services to ensure smooth clearance and compliance.',
      features: ['Documentation handling', 'Duty optimization', 'Trade compliance', 'Regulatory guidance']
    },
    {
      icon: Users,
      title: 'Supply Chain Consultation',
      description: 'Strategic logistics consulting to optimize your supply chain efficiency and reduce costs.',
      features: ['Process optimization', 'Cost analysis', 'Risk management', 'Technology integration']
    }
  ];

  const benefits = [
    {
      icon: Shield,
      title: 'Secure & Insured',
      description: 'Comprehensive insurance coverage and security protocols protect your cargo.'
    },
    {
      icon: Clock,
      title: '24/7 Support',
      description: 'Round-the-clock customer support and real-time shipment monitoring.'
    },
    {
      icon: Globe,
      title: 'Global Network',
      description: 'Extensive partner network ensuring reliable service in 150+ countries.'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-24 bg-navy text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                Comprehensive Logistics Solutions
              </h1>
              <p className="text-xl text-silver">
                From air and sea freight to warehousing and customs clearance, we provide end-to-end logistics services tailored to your business needs.
              </p>
              <Button size="lg" className="bg-white text-navy hover:bg-silver">
                <Link to="/quote" className="flex items-center">
                  Get Custom Quote
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
            <div className="relative">
              <img 
                src={servicesImage} 
                alt="Logistics services overview" 
                className="w-full h-96 object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Our Services</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Complete logistics solutions designed to meet your international trade requirements
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="card-elevated h-full">
                <CardHeader>
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                    <service.icon className="h-6 w-6" />
                  </div>
                  <CardTitle className="text-xl">{service.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm">
                        <div className="w-2 h-2 bg-primary rounded-full mr-3" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Why Choose Our Services?</h2>
            <p className="text-xl text-muted-foreground">
              Experience the difference with our commitment to excellence and customer satisfaction
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="card-elevated text-center">
                <CardContent className="p-8 space-y-4">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                    <benefit.icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground">{benefit.title}</h3>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl font-bold text-foreground">
              Ready to Streamline Your Logistics?
            </h2>
            <p className="text-xl text-muted-foreground">
              Let our experts design a custom logistics solution that fits your business needs and budget.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">
                <Link to="/quote">Request Quote</Link>
              </Button>
              <Button size="lg" variant="outline">
                <Link to="/contact">Speak with Expert</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;