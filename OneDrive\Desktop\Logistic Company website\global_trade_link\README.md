# 🌍 GlobalTradeLink – International Import/Export & Tracking Platform

## 📦 Project Info

**Project Name**: GlobalTradeLink  
**Project Description**:  
A modern, responsive web application for managing international import/export logistics. It includes a real-time shipment tracking system for customers to monitor cargo status from dispatch to delivery.

---

## 🛠 How to Edit This Project

You can develop and maintain this project using your local IDE.

### ✅ Prerequisites

- Node.js and npm (Recommended: use [nvm](https://github.com/nvm-sh/nvm#installing-and-updating))
- Git installed

### 🔧 Setup Instructions

```bash
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate into the project directory
cd globaltradelink

# Step 3: Install dependencies
npm install

# Step 4: Start the development server
npm run dev


